<h1 align="center">Fastify</h1>

## Core Documents
<a id="reference-core-docs"></a>

For the full table of contents (TOC), see [below](#reference-toc). The following
list is a subset of the full TOC that detail core Fastify APIs and concepts in
order of most likely importance to the reader:

+ [Server](./Server.md): Documents the core Fastify API. Includes documentation
  for the factory function and the object returned by the factory function.
+ [Lifecycle](./Lifecycle.md): Explains the Fastify request lifecycle and
  illustrates where [Hooks](./Hooks.md) are available for integrating with it.
+ [Routes](./Routes.md): Details how to register routes with Fastify and how
  Fastify builds and evaluates the routing trie.
+ [Request](./Request.md): Details Fastify's request object that is passed into
  each request handler.
+ [Reply](./Reply.md): Details Fastify's response object available to each
  request handler.
+ [Validation and Serialization](./Validation-and-Serialization.md): Details
  Fastify's support for validating incoming data and how Fastify serializes data
  for responses.
+ [Plugins](./Plugins.md): Explains Fastify's plugin architecture and API.
+ [Encapsulation](./Encapsulation.md): Explains a core concept upon which all
  Fastify plugins are built.
+ [Decorators](./Decorators.md): Explains the server, request, and response
  decorator APIs.
+ [Hooks](./Hooks.md): Details the API by which Fastify plugins can inject
  themselves into Fastify's handling of the request lifecycle.


## Reference Documentation Table Of Contents
<a id="reference-toc"></a>

This table of contents is in alphabetical order.

+ [Content Type Parser](./ContentTypeParser.md): Documents Fastify's default
  content type parser and how to add support for new content types.
+ [Decorators](./Decorators.md): Explains the server, request, and response
  decorator APIs.
+ [Encapsulation](./Encapsulation.md): Explains a core concept upon which all
  Fastify plugins are built.
+ [Errors](./Errors.md): Details how Fastify handles errors and lists the
  standard set of errors Fastify generates.
+ [Hooks](./Hooks.md): Details the API by which Fastify plugins can inject
  themselves into Fastify's handling of the request lifecycle.
+ [HTTP2](./HTTP2.md): Details Fastify's HTTP2 support.
+ [Lifecycle](./Lifecycle.md): Explains the Fastify request lifecycle and
  illustrates where [Hooks](./Hooks.md) are available for integrating with it.
+ [Logging](./Logging.md): Details Fastify's included logging and how to
  customize it.
+ [Long Term Support](./LTS.md): Explains Fastify's long term support (LTS)
  guarantee and the exceptions possible to the [semver](https://semver.org)
  contract.
+ [Middleware](./Middleware.md): Details Fastify's support for Express.js style
  middleware.
+ [Plugins](./Plugins.md): Explains Fastify's plugin architecture and API.
+ [Reply](./Reply.md): Details Fastify's response object available to each
  request handler.
+ [Request](./Request.md): Details Fastify's request object that is passed into
  each request handler.
+ [Routes](./Routes.md): Details how to register routes with Fastify and how
  Fastify builds and evaluates the routing trie.
+ [Server](./Server.md): Documents the core Fastify API. Includes documentation
  for the factory function and the object returned by the factory function.
+ [TypeScript](./TypeScript.md): Documents Fastify's TypeScript support and
  provides recommendations for writing applications in TypeScript that utilize
  Fastify.
+ [Validation and Serialization](./Validation-and-Serialization.md): Details
  Fastify's support for validating incoming data and how Fastify serializes data
  for responses.
+ [Warnings](./Warnings.md): Details the warnings Fastify emits and how to
  solve them.
