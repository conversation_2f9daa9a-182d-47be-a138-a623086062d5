{"name": "@fastify/ajv-compiler", "version": "4.0.2", "description": "Build and manage the AJV instances for the fastify framework", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "directories": {"test": "test"}, "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "unit": "tap", "test": "npm run unit && npm run test:typescript", "test:typescript": "tsd", "ajv:compile": "ajv compile -s test/source.json -o test/validate_schema.js"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/ajv-compiler.git"}, "keywords": ["ajv", "validator", "schema", "compiler", "fastify"], "author": "<PERSON> <<EMAIL>> (https://github.com/Eomm)", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://james.sumners.info"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/ajv-compiler/issues"}, "homepage": "https://github.com/fastify/ajv-compiler#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"ajv-cli": "^5.0.0", "ajv-errors": "^3.0.0", "ajv-i18n": "^4.2.0", "ajv-merge-patch": "^5.0.1", "cronometro": "^4.0.0", "eslint": "^9.17.0", "fastify": "^5.0.0", "neostandard": "^0.12.0", "require-from-string": "^2.0.2", "sanitize-filename": "^1.6.3", "tap": "^19.0.0", "tsd": "^0.31.0"}, "dependencies": {"ajv": "^8.12.0", "ajv-formats": "^3.0.1", "fast-uri": "^3.0.0"}}