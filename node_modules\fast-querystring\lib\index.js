"use strict";

const parse = require("./parse");
const stringify = require("./stringify");

const fastQuerystring = {
  parse,
  stringify,
};

/**
 * Enable TS and JS support
 *
 * - `const qs = require('fast-querystring')`
 * - `import qs from 'fast-querystring'`
 */
module.exports = fastQuerystring;
module.exports.default = fastQuerystring;
module.exports.parse = parse;
module.exports.stringify = stringify;
